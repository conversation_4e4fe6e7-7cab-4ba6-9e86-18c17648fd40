import mongoose, { Schema, Document } from 'mongoose';

     export interface IUser extends Document {
       name: string;
       email: string;
       oauthProvider: string;
       oauthId: string;
       location: string;
       points: number;
       joinDate: Date;
       avatar?: string;
     }

     const UserSchema: Schema = new Schema({
       name: { type: String, required: true },
       email: { type: String, required: true, unique: true },
       oauthProvider: { type: String, required: true },
       oauthId: { type: String, required: true, unique: true },
       location: { type: String, required: true },
       points: { type: Number, default: 50 },
       joinDate: { type: Date, default: Date.now },
       avatar: { type: String },
     });

     export default mongoose.model<IUser>('User', UserSchema);