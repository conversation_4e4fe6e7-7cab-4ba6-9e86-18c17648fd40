import express from 'express';
     import passport from 'passport';

     const router = express.Router();

     // Initiate Google OAuth
     router.get('/google', passport.authenticate('google', { scope: ['profile', 'email'] }));

     // Handle Google OAuth callback
     router.get(
       '/google/callback',
       passport.authenticate('google', { session: false }),
       (req: any, res) => {
         const { user, token } = req.user;
         // Redirect to frontend with token
         res.redirect(`http://localhost:5173/auth/callback?token=${token}`);
       }
     );

     // Get current user
     router.get('/me', (req, res) => {
       if (req.user) {
         res.json(req.user);
       } else {
         res.status(401).json({ error: 'Not authenticated' });
       }
     });

     export default router;