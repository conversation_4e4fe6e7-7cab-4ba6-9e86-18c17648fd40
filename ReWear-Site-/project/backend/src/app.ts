import express from 'express';
     import cors from 'cors';
     import dotenv from 'dotenv';
     import connectDB from './config/database';
     import passport from 'passport';
     import './config/passport'; // Initialize Passport
     import authRoutes from './routes/authRoutes';

     dotenv.config();

     const app = express();

     // Middleware
     app.use(cors({ origin: 'http://localhost:5173' }));
     app.use(express.json());
     app.use(passport.initialize());

     // Connect to MongoDB
     connectDB();

     // Routes
     app.use('/api/auth', authRoutes);

     // Basic route for testing
     app.get('/api/health', (req, res) => {
       res.json({ status: 'OK', message: 'ReWear backend is running' });
     });

     const PORT = process.env.PORT || 5000;
     app.listen(PORT, () => {
       console.log(`Server running on port ${PORT}`);
     });

     export default app;