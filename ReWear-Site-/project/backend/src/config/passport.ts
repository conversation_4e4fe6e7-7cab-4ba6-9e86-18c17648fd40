import passport from 'passport';
     import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
     import User, { IUser } from '../models/User';
     import jwt from 'jsonwebtoken';

     passport.use(
       new GoogleStrategy(
         {
           clientID: process.env.GOOGLE_CLIENT_ID!,
           clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
           callbackURL: process.env.GOOGLE_CALLBACK_URL!,
         },
         async (accessToken, refreshToken, profile, done) => {
           try {
             let user = await User.findOne({ oauthProvider: 'google', oauthId: profile.id });
             if (!user) {
               user = new User({
                 name: profile.displayName,
                 email: profile.emails?.[0].value,
                 oauthProvider: 'google',
                 oauthId: profile.id,
                 location: 'Unknown', // Update later via user profile
                 avatar: profile.photos?.[0].value,
               });
               await user.save();
             }
             const token = jwt.sign({ id: user._id, email: user.email }, process.env.JWT_SECRET!, { expiresIn: '1d' });
             return done(null, { user, token });
           } catch (error) {
             return done(error, false);
           }
         }
       )
     );

     passport.serializeUser((userObj: any, done) => {
       done(null, userObj);
     });

     passport.deserializeUser((userObj: any, done) => {
       done(null, userObj);
     });